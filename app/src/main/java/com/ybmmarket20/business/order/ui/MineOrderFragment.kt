package com.ybmmarket20.business.order.ui

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.viewpager.widget.ViewPager
import com.app.hubert.guide.NewbieGuide
import com.app.hubert.guide.core.Controller
import com.app.hubert.guide.model.GuidePage
import com.gyf.immersionbar.ktx.immersionBar
import com.jeremyliao.liveeventbus.LiveEventBus
import com.ybmmarket20.R
import com.ybmmarket20.activity.LoginActivity
import com.ybmmarket20.activity.SearchOrderActivity
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.PurchaseSwitchBean
import com.ybmmarket20.bean.PushNoticeConfigBean
import com.ybmmarket20.business.order.adapter.OrderListPagerAdapter
import com.ybmmarket20.business.order.bean.OrderFilterBean
import com.ybmmarket20.common.LiveEventBusManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.isLogin
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.constant.IntentCanst.ACTION_SWITCH_USER
import com.ybmmarket20.utils.NotificationPermissionUtils
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.PushTipsDialog
import com.ybmmarket20.viewmodel.SwitchViewModel
import com.ybmmarket20.xyyreport.page.orderList.OrderListReport
import com.ybmmarket20.xyyreport.page.orderList.OrderlistConstant
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarketkotlin.bean.BannerType
import com.ybmmarketkotlin.bean.OrderTopBannerBean
import com.ybmmarketkotlin.views.OrderTopBannerLayout
import kotlinx.android.synthetic.main.fragment_all_order.cl_search
import kotlinx.android.synthetic.main.fragment_all_order.llFilter
import kotlinx.android.synthetic.main.fragment_all_order.ll_bill
import kotlinx.android.synthetic.main.fragment_all_order.ll_title
import kotlinx.android.synthetic.main.fragment_all_order.llInVoice
import kotlinx.android.synthetic.main.fragment_all_order.order_banner
import kotlinx.android.synthetic.main.fragment_all_order.ps_tab
import kotlinx.android.synthetic.main.fragment_all_order.viewPushTipsBottom
import kotlinx.android.synthetic.main.fragment_all_order.vp
import androidx.core.content.edit


/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2024/11/29 10:52
 *    desc   :
 */
class MineOrderFragment : MineOrderAnalysisFragment() {
    private var adapter: OrderListPagerAdapter? = null
    private var isFirst: Boolean = true
    private var order_state: String? = null
    private val switchViewModel: SwitchViewModel by viewModels()
    private var br: BroadcastReceiver? = null
    private var orderListFragments = mutableListOf<OrderListFragment>()
    private var dialogPushTips: PushTipsDialog? = null
    private var mNoticeConfig: PushNoticeConfigBean? = null
    private var isFinishedOrderGuide = 0

    /**
     * 筛选过滤
     */
    private var mPopFilter: OrderFilterPopWindow? = null
    var mTimeItemId: Int = 0
    var mQuickItemId: Int = 0
    var mStartTime: String = ""
    var mEndTime: String = ""
    var mTimeFieldName: String = ""  // 时间筛选项，传给后台的key。由后台下发回传
    var mQuickFieldName: String = ""// 快速筛选项，传给后台的key。由后台下发回传
    private lateinit var currBannerType: BannerType
    private var bannerItemList = ArrayList<OrderTopBannerBean>()

    companion object {
        const val GUIDE_ORDER_FILTER = "guide_order_filter"
        const val ORDER_GUIDE_LABEL = "guide_order_label"
    }

    override fun getOrderListAnalysisParams(): IOrderListAnalysisParams? {
        return try {
            orderListFragments[vp.currentItem]
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    override fun getVpCurrIndex(): Int {
        return vp?.currentItem ?: 0
    }

    fun setPositionListener(position: Int) {
        when (position) {
            1 -> {
                LocalBroadcastManager.getInstance(requireActivity()).sendBroadcast(Intent(IntentCanst.ACTION_HIDE_ORDER_BUBBLE))
            }
        }
    }

    private fun initSwitchStatus() {
        switchViewModel.purchaseSwitchStatusLiveData.observe(
            this,
            Observer<BaseBean<PurchaseSwitchBean>> { integerBaseBean: BaseBean<PurchaseSwitchBean> ->
                if (integerBaseBean.isSuccess && integerBaseBean.data != null && integerBaseBean.data?.purchaseOrderSwitch == 1) {
                    ll_bill.visibility = View.VISIBLE
                }
            })
        switchViewModel.getPurchaseSwitchStatus()

    }

    private fun initGetOrderBubbleCount() {
        LiveEventBus.get(LiveEventBusManager.OrderBus.NO_PAY_ORDERS_BUBBLE, Boolean::class.java)
            .observe(this) {
                switchViewModel.getOrdersBubbleCount()
            }
        switchViewModel.ordersBubbleCountData?.observe(requireActivity()) { t ->
            if (t.isSuccess) {
                val data = t.data
                if (data.waitPayNum > 0) {
                    ps_tab.showMsg(1, data.waitPayNum)
                } else {
                    ps_tab.hideMsg(1)
                }
                if (data.needProcessRefundNum > 0) {
                    ps_tab.showMsg(5, data.needProcessRefundNum)
                } else {
                    ps_tab.hideMsg(5)
                }

            }
        }
    }

    //显示新手引导页
    private fun showNewbieGuidePage() {
        if (isFinishedOrderGuide!=0) return
        val sharedPreferences = requireActivity().getSharedPreferences(NewbieGuide.TAG, Activity.MODE_PRIVATE)
        sharedPreferences.edit { putInt(ORDER_GUIDE_LABEL, 0) }
        NewbieGuide.with(this)
            .setLabel(ORDER_GUIDE_LABEL)
            .addGuidePage(
                GuidePage.newInstance().setLayoutRes(R.layout.guide_order_invoice)
                    .setEverywhereCancelable(false)
                    .setOnLayoutInflatedListener { view: View, controller: Controller ->
                        val clRoot = view.findViewById<ConstraintLayout>(R.id.cl_root)

                        clRoot.setOnClickListener {
                            controller.showPage(1)
                        }

                    })
            .addGuidePage(
                GuidePage.newInstance().setLayoutRes(R.layout.guide_order_filter)
                    .setEverywhereCancelable(false)
                    .setOnLayoutInflatedListener { view: View, controller: Controller ->
                        val clRoot = view.findViewById<ConstraintLayout>(R.id.cl_root)
                        clRoot.setOnClickListener {
                            newBieGuideFinished(controller)
                        }
                    }).show()
    }

    private fun newBieGuideFinished(controller: Controller) {
        controller.remove()
        isFinishedOrderGuide = 1
        SpUtil.writeInt(GUIDE_ORDER_FILTER, 1)
    }

    private fun initOrderTopBanner() {
        switchViewModel.getOrderAdRotationLiveData.observe(requireActivity()) {
            bannerItemList = ArrayList<OrderTopBannerBean>()
            bannerItemList.clear()
            if (it.isSuccess && it.data != null) {
                val data = it.data
                if (data.licenseRemind?.data != null) {
                    bannerItemList.add(
                        OrderTopBannerBean(
                            BannerType.CUSTOM_VIEW_APTITUDE, licenseRemind = data.licenseRemind
                        )
                    )
                }
                if (data.consumeRebateDetail?.data != null) {
                    bannerItemList.add(
                        OrderTopBannerBean(
                            BannerType.CUSTOM_VIEW_REBATE,
                            consumeRebateDetail = data.consumeRebateDetail
                        )
                    )
                }
                if (data.rechargeDiscount?.data != null && !data.rechargeDiscount.data.highLevelRedPacketMsgLowerHalf.isNullOrEmpty() && !data.rechargeDiscount.data.highLevelRedPacketMsgUpperHalf.isNullOrEmpty()) {
                    bannerItemList.add(
                        OrderTopBannerBean(
                            BannerType.CUSTOM_VIEW_RED_PACK,
                            rechargeDiscount = data.rechargeDiscount
                        )
                    )
                }
                if (data.sceneResponse?.data != null) {
                    setCouponId(data.sceneResponse.data)
                    bannerItemList.add(
                        OrderTopBannerBean(
                            BannerType.CUSTOM_VIEW_COUPON, sceneResponse = data.sceneResponse
                        )
                    )
                }
                if (data.saasAd?.data != null) {
                    bannerItemList.add(
                        OrderTopBannerBean(
                            BannerType.IMAGE, saasAd = data.saasAd
                        )
                    )
                }
            } else {
                order_banner.visibility = View.GONE
            }
            // 根据level进行排序
            bannerItemList.sortWith(compareBy { bannerItem ->
                when (bannerItem.type) {
                    BannerType.CUSTOM_VIEW_APTITUDE -> bannerItem.licenseRemind?.level
                    BannerType.CUSTOM_VIEW_REBATE -> bannerItem.consumeRebateDetail?.level
                    BannerType.CUSTOM_VIEW_RED_PACK -> bannerItem.rechargeDiscount?.level
                    BannerType.CUSTOM_VIEW_COUPON -> bannerItem.sceneResponse?.level
                    BannerType.IMAGE -> bannerItem.saasAd?.level
                    else -> 0
                } ?: 0
            })
            order_banner.setBannerClickCallBack(object :
                OrderTopBannerLayout.OnBannerClickCallBack {
                override fun onViewClicked(
                    type: BannerType,
                    item: OrderTopBannerBean?,
                    couponClaimed: Int?,
                    couponId: String?,
                    h5Url: String?
                ) {
                    onTrackOrderBannerClick(type)
                    when (type) {
                        BannerType.CUSTOM_VIEW_APTITUDE -> {
                            RoutersUtils.open("ybmpage://aptitude")
                        }

                        BannerType.CUSTOM_VIEW_REBATE -> {
                            if (item?.consumeRebateDetail?.data?.appUrls.isNullOrEmpty()) return
                            RoutersUtils.open("ybmpage://commonh5activity?url=${item?.consumeRebateDetail?.data?.appUrls}")
                        }

                        BannerType.CUSTOM_VIEW_RED_PACK -> {
                            RoutersUtils.open("ybmpage://myvirtualmoney?showRechargeDialog=1")
                        }

                        BannerType.CUSTOM_VIEW_COUPON -> {
                            when (couponClaimed) {
                                1 -> {
                                    switchViewModel.getVoucher(couponId, h5Url)
                                }

                                2 -> RoutersUtils.open(h5Url)
                                else -> {}
                            }
                        }

                        BannerType.IMAGE -> {
                            //saas h5页
                            if (item?.saasAd?.data?.saasUrl.isNullOrEmpty()) return
                            RoutersUtils.open(item?.saasAd?.data?.saasUrl)
                        }
                    }
                }

                override fun onBannerExposure(type: BannerType) {
                    currBannerType = type
                    onTrackOrderBannerExposure(type)
                }
            })

            if (bannerItemList.size > 0) {
                order_banner.visibility = View.VISIBLE
                order_banner.setData(bannerItemList)
                onTrackOrderBannerExposure(bannerItemList[0].type)
                currBannerType = bannerItemList[0].type
            } else {
                order_banner.visibility = View.GONE
            }
        }
    }

    private fun initSceneData() {
        switchViewModel.voucherLiveData.observe(requireActivity()) {
            val mUrl = it.first
            val mBean = it.second
            if (mBean.isSuccess) {
                RoutersUtils.open(mUrl)
            }
        }
    }

    override fun onVisibleChanged(isVisible: Boolean) {
        super.onVisibleChanged(isVisible)
        if (isVisible) {
            switchViewModel.getOrdersBubbleCount()
            switchViewModel.getOrderAdRotation()
        } else {
            pauseBanner()
        }
    }

    private fun pauseBanner() {
        order_banner?.stop()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initBroadCastReceiver()
    }

    /**
     * 初始化广播
     */
    private fun initBroadCastReceiver() {
        br = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                if (ACTION_SWITCH_USER == intent.action) {
                    changeTab("0")
                    initData("")
                }
            }
        }
        val intentFilter = IntentFilter(ACTION_SWITCH_USER)
        LocalBroadcastManager.getInstance(notNullActivity)
            .registerReceiver(br as BroadcastReceiver, intentFilter)
    }

    override fun initData(content: String?) {
        if (!isLogin()) {
            //未登录
            val it = Intent(context, LoginActivity::class.java)
            startActivity(it)
            return
        }
        initSwitchStatus()
        initGetOrderBubbleCount()
        isFinishedOrderGuide =  SpUtil.readInt(GUIDE_ORDER_FILTER, 0)
        showNewbieGuidePage()
        initOrderTopBanner()
        initSceneData()
//        order_state = getIntent().getStringExtra(IntentCanst.ORDER_STATE)
        val fragment1 = OrderListFragment.getInstance(0, "全部")
        val fragment7 = OrderListFragment.getInstance(10, "待支付")
        val fragment2 = OrderListFragment.getInstance(1, "待配送")
        val fragment3 = OrderListFragment.getInstance(2, "配送中")
        //        OrderListFragment fragment5 = OrderListFragment.getInstance(4, "待收货");
        val fragment4 = OrderListFragment.getInstance(3, "完成")
        val fragment6 = OrderListFragment.getInstance(101, "待评价") //101请勿动
        val fragment8 = OrderListFragment.getInstance(90, "退款/售后")
        orderListFragments = ArrayList<OrderListFragment>()
        orderListFragments.add(fragment1)
        orderListFragments.add(fragment7)
        orderListFragments.add(fragment2)
        orderListFragments.add(fragment3)
        //        orderListFragments.add(fragment5);
        orderListFragments.add(fragment4)
        orderListFragments.add(fragment8)
        orderListFragments.add(fragment6)
        adapter = OrderListPagerAdapter(childFragmentManager, orderListFragments)
        vp.adapter = adapter
        vp.setScroll(false)
        vp.offscreenPageLimit = orderListFragments.size + 1
        ps_tab.setViewPager(vp)
        ps_tab.setIndicatorWidthEqualTitleHalf(true)
        ps_tab.addUnrelatedVpIndex(orderListFragments.indexOf(fragment8))
        ps_tab.setOnUnrelatedVpIndexCallback { index: Int ->
            RoutersUtils.open(
                "ybmpage://refundoraftersales"
            )
        }

        changeTab(order_state)
        vp.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(
                position: Int, positionOffset: Float, positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                setPositionListener(position)
                SpmUtil.checkAnalysisContext(requireActivity()) {
                    val isTrackPv =
                        it.getExtensionValue(OrderlistConstant.ORDER_LIST_TRACK_PV_SWITCH)
                    if (isTrackPv == null) {
                        orderListFragments[position].pvTrack()
                    } else {
                        val isTrackPvBoolean = isTrackPv as Boolean
                        if (isTrackPvBoolean) {
                            orderListFragments[position].pvTrack()
                        } else {
                            it.putExtension(OrderlistConstant.ORDER_LIST_TRACK_PV_SWITCH, true)
                        }
                    }
                    onTrackOrderBannerExposure(currBannerType)
                }
            }

            override fun onPageScrollStateChanged(state: Int) {}
        })

        ll_bill.setOnClickListener {
            RoutersUtils.open("ybmpage://purchasereconciliation")
            val map = HashMap<String, String>()
            map["text"] = "我的账单"
            XyyIoUtil.track("page_OrderList_purchaseOrder", map)
        }
        llFilter.setOnClickListener {
            if (mPopFilter == null) {
                mPopFilter = OrderFilterPopWindow(requireActivity())
                mPopFilter?.dataListener { timeItemId, quickItemId, startTime, endTime ->
                    mTimeItemId = timeItemId
                    mQuickItemId = quickItemId
                    mStartTime = startTime
                    mEndTime = endTime
                    // 取消了所有筛选条件，不搜索
                    if (timeItemId == 0 && quickItemId == 0 && mStartTime.isNullOrEmpty()) {
                        return@dataListener
                    }
                    Intent(context, SearchOrderActivity::class.java).apply {
                        putExtra(IntentCanst.ORDER_SEARCH_TIME_FIELD, mTimeFieldName)
                        putExtra(IntentCanst.ORDER_SEARCH_TIME_ID, mTimeItemId)
                        putExtra(IntentCanst.ORDER_SEARCH_QUICK_FIELD, mQuickFieldName)
                        putExtra(IntentCanst.ORDER_SEARCH_QUICK_ID, mQuickItemId)
                        putExtra(IntentCanst.ORDER_SEARCH_START_TIME, startTime)
                        putExtra(IntentCanst.ORDER_SEARCH_END_TIME, endTime)
                        startActivity(this)
                    }
                }
                switchViewModel.orderFilterOptions.observe(this) {
                    if (it.isSuccess) {
                        val timeDatas =
                            it.data.filter { OrderFilterBean.ITEM_TYPE_TIME == it.itemType }
                        if (timeDatas.isEmpty()) {
                            mPopFilter = null
                            return@observe
                        }
                        mTimeFieldName = timeDatas[0].field
                        val quickDatas =
                            it.data.filter { OrderFilterBean.ITME_TYPE_QUICK == it.itemType }
                        if (quickDatas.isEmpty()) {
                            mPopFilter = null
                            return@observe
                        }
                        mQuickFieldName = quickDatas[0].field
                        mPopFilter?.setData(timeDatas[0].itemList, quickDatas[0].itemList)
                        mPopFilter?.show(llFilter)
                    } else {
                        mPopFilter = null
                    }
                }
                switchViewModel.queryFilterOptions()
            } else {
                mPopFilter?.setDefault(mTimeItemId, mQuickItemId, mStartTime, mEndTime)
                mPopFilter?.show(llFilter)
            }
        }
        cl_search.setOnClickListener {
            OrderListReport.trackSearchClick(requireActivity())
            val intent = Intent(context, SearchOrderActivity::class.java)
            startActivity(intent)
        }
        llInVoice.setOnClickListener {
            RoutersUtils.open("ybmpage://editInvoiceInfoActivity")
        }
        pvTrackFirst()
    }

    override fun initTitle() {
    }

    override fun getParams() = RequestParams()

    override fun getUrl() = null

    override fun getLayoutId() = R.layout.fragment_all_order

    override fun onResume() {
        super.onResume()
        if (NotificationPermissionUtils.areNotificationsEnabled(requireActivity())) {
            dialogPushTips?.dismiss()
            viewPushTipsBottom.visibility = View.GONE
        } else {
            if (isFirst) {
                isFirst = false
                dialogPushTips = PushTipsDialog(requireActivity())
                switchViewModel.pushNoticeConfig.observe(this) { configBean ->
                    mNoticeConfig = configBean.data
                    dialogPushTips?.show(configBean.data.strongRemind)
                    viewPushTipsBottom.show(
                        configBean.data.weakRemind,
                        configBean.data.strongRemind
                    )
                }
                switchViewModel.getSysNoticeConfig()
            } else {
                if (mNoticeConfig != null) {
                    viewPushTipsBottom.show(
                        mNoticeConfig!!.weakRemind,
                        mNoticeConfig!!.strongRemind
                    )
                }
            }
        }
        immersionBar {
            fitsSystemWindows(false)
            titleBar(ll_title)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        br?.let {
            LocalBroadcastManager.getInstance(notNullActivity).unregisterReceiver(it)
        }
    }

    fun changeTab(state: String?) {
        var position = when (state) {
            "0" -> 0
            "10" -> 1
            "1" -> 2
            "2" -> 3
            "3" -> 4
            "101" -> 6
            "90" -> 5
            else -> 0
        }
        vp.setCurrentItem(position, false)
    }

    fun getTabInt(state: String?): Int {
        return when (state) {
            "0" -> 0
            "10" -> 1
            "1" -> 2
            "2" -> 3
            "3" -> 4
            "101" -> 6
            "90" -> 5
            else -> 0
        }
    }
}