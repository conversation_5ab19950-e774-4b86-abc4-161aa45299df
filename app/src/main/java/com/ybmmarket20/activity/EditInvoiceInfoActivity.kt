package com.ybmmarket20.activity

import android.view.View
import androidx.activity.viewModels
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.bean.InvoiceEditHistoryBean
import com.ybmmarket20.common.vm.BaseVMActivity
import com.ybmmarket20.databinding.ActivityInvoiceInfoBinding
import com.ybmmarket20.viewmodel.EditInvoiceViewModel

/**
 *    author : hcq
 *    date   : 2025/9/18 15:40
 *    desc   : 发票信息页面
 */
@Router("editInvoiceInfoActivity")
class EditInvoiceInfoActivity :
    BaseVMActivity<ActivityInvoiceInfoBinding, EditInvoiceViewModel>(ActivityInvoiceInfoBinding::inflate) {
    override val viewModel: EditInvoiceViewModel by viewModels()
    override fun initData() {
        setTitle("发票信息")

        // 获取Intent参数
        getIntentParams()

        // 初始化UI
        initViews()

        // 设置观察者
        setupObservers()
    }

    /**
     * 获取Intent参数
     */
    private fun getIntentParams() {

    }

    /**
     * 初始化视图
     */
    private fun initViews() {

    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        val data = getMockData()

        if(data == null){
            showEmptyView()
            return
        }else{
            vBinding.rvEditHistory.visibility = View.VISIBLE
            vBinding.llEmptyView.visibility = View.GONE
        }

        // 使用自定义View的bindData方法来处理显示隐藏逻辑
        vBinding.invoiceItem.bindData(data)
    }

    /**
     * 显示空状态视图
     */
    private fun showEmptyView() {
        vBinding.rvEditHistory.visibility = View.GONE
        vBinding.llEmptyView.visibility = View.VISIBLE
    }

    /**
     * 获取模拟数据
     */
    private fun getMockData(): InvoiceEditHistoryBean {
        return InvoiceEditHistoryBean(
            id = "1",
            editTime = "2025年9月18日 13:30",
            editStatus = 2,
            editStatusName = "审核通过",
            operator = "系统",
            remark = "审核通过",
            invoiceType = "增值税专用发票",
            companyName = "辽宁市西安区永和大药房",
            taxNumber = "91420112MA4KLGHW01",
            address = "武汉市东湖新技术开发区关东大道77号金融港后台服务中心一期A1栋第3层301室（注册地址：武汉市武昌区）",
            phone = "027-********",
            bankName = "中国建设银行武汉白沙洲支行",
            bankAccount = "62043098712004076353"
        )
    }

    /**
     * 获取部分数据为空的模拟数据（用于测试显示隐藏逻辑）
     */
    private fun getMockDataWithEmptyFields(): InvoiceEditHistoryBean {
        return InvoiceEditHistoryBean(
            id = "2",
            editTime = "2025年9月18日 14:30",
            editStatus = 1,
            editStatusName = "待审核",
            operator = "用户",
            remark = "待审核",
            invoiceType = "增值税普通发票",
            companyName = "测试公司名称",
            taxNumber = "91420112MA4KLGHW02",
            address = "", // 地址为空
            phone = "", // 电话为空
            bankName = "", // 开户银行为空
            bankAccount = "" // 银行账号为空
        )
    }
}