package com.ybmmarket20.activity;

import static com.ybmmarket20.activity.AddAptitudeActivity.APTITUDE_DETAIL_ACTIVITY;
import static com.ybmmarket20.activity.AddAptitudeActivity.APTITUDE_LIST_ACTIVITY;
import static com.ybmmarket20.activity.AddAptitudeActivity.APTITUDE_REQUEST_CODE;
import static com.ybmmarket20.activity.AptitudeActivity.APTITUDE_FIRSTTYPE;

import android.content.Intent;
import android.text.InputFilter;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatEditText;
import androidx.core.content.ContextCompat;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.AptitudeAthentication;
import com.ybmmarket20.bean.AptitudeBasicInfo;
import com.ybmmarket20.bean.AptitudeBasicInfoBean;
import com.ybmmarket20.bean.AptitudeBasicInfoExtrasBean;
import com.ybmmarket20.bean.AptitudeDetailEditData;
import com.ybmmarket20.bean.AptitudeInitBean;
import com.ybmmarket20.bean.AptitudeInvoiceTypeBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CustomerTypeBean;
import com.ybmmarket20.bean.LicenceBean;
import com.ybmmarket20.bean.Province;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.AptitudeProgressEntry;
import com.ybmmarket20.view.AptitudeProgressView;
import com.ybmmarket20.view.ButtonObserver;
import com.ybmmarket20.view.jdaddressselector.AddressProvider;
import com.ybmmarket20.view.jdaddressselector.BottomDialog;
import com.ybmmarket20.view.jdaddressselector.OnAddressSelectedListener;
import com.ybmmarket20.view.picker.PickerManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Pattern;

import butterknife.Bind;
import butterknife.OnClick;

@Router({"aptitudebasicinfo", "aptitudebasicinfo/:licenseStatus", "aptitudebasicinfo/:licenseStatus/:isEdit"})
public class AddAptitudeBasicInfoActivity extends BaseActivity implements OnAddressSelectedListener {


    @Bind(R.id.ll_top_tips)
    LinearLayout llTopTips;
    @Bind(R.id.ll_invoice_info_container)
    LinearLayout llInvoiceInfoContainer;
    @Bind(R.id.et_name)
    AppCompatEditText etName;
    @Bind(R.id.et_type)
    AppCompatEditText etType;
    @Bind(R.id.et_invoice_type)
    AppCompatEditText etInvoiceType;
    @Bind(R.id.et_address)
    AppCompatEditText etAddress;
    @Bind(R.id.et_detail_address)
    AppCompatEditText etDetailAddress;
    @Bind(R.id.btn_submit)
    ButtonObserver btnSubmit;
    ButtonObserver btnSubmitAuthentication;
    AptitudeProgressView progressView;
    AppCompatEditText etCode;
    boolean codeType = true; //true: 营业执照编码.false:医疗机构执业许可证编码

    private String mCurrentProviceName = "";
    private String mCurrentCityName = "";
    private String mCurrentDistrictName = "";
    private String mCurrentStreetName = "";
    private String mCurrentProvinceId = "";
    private String mCurrentCityId = "";
    private String mCurrentDistrictId = "";
    private String mCurrentStreetId = "";
    private AptitudeBasicInfoExtrasBean extrasBean;
    private BottomDialog dialog;

    //资质状态
    private String merchantId = "";
    private int licenseStatus = APTITUDE_FIRSTTYPE;
    // 是否是从一审驳回编辑进入 1：是，0：否
    private int isEdit = 0;
    private int firstCustomerType;//首次企业类型（客户类型） 默认单体药店
    private String firstCustomerTypeName;//首次客户类型名称

    private int mCustomerTypeId;//客户类型id
    private int mCustomerTypeIndex = 0;//默认选中客户类型
    private List<CustomerTypeBean> customerTypeList = new ArrayList<>();

    private int mSelectedInvoiceTypeIndex = 0;
    private int mSelectedInvoiceType;
    private List<AptitudeInvoiceTypeBean> invoiceTypeList = new ArrayList<>();
    private int progressPosition = 0;
    private TextView typeTitle;
    private TextView codeTitle;
    private LinearLayout llUserInfo, llAuthentication;
    private ImageView ivBack;
    private String ecOrgCode;
    private AptitudeDetailEditData extra;
    private int from; //1: 关联店铺店长

    @Override
    protected int getContentViewId() {
        getArgument();
        if (isAptitudeFirst()) {
            return R.layout.activity_add_aptitude_basic_info2;
        } else {
            return R.layout.activity_add_aptitude_basic_info;
        }
    }

    /**
     * 获取上个页面传入的参数
     */
    private void getArgument() {
        try {
            String licenseStatusStr = getIntent().getStringExtra("licenseStatus");
            String isEditStr = getIntent().getStringExtra("isEdit");
            ecOrgCode = getIntent().getStringExtra("ecOrgCode");
            if (!TextUtils.isEmpty(licenseStatusStr)) {
                licenseStatus = Integer.parseInt(licenseStatusStr);
            }
            if (!TextUtils.isEmpty(isEditStr)) {
                isEdit = Integer.parseInt(isEditStr);
            }
            extra = getIntent().getParcelableExtra("aptitudeDetailEditData");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void initData() {
        merchantId = SpUtil.getMerchantid();
        getArgument();
        try {
            String fromStr = getIntent().getStringExtra("from");
            from = Integer.parseInt(fromStr);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (licenseStatus == APTITUDE_FIRSTTYPE) {
            //首营资质
            setTitle("添加首营资质");
            progressView = findViewById(R.id.apv);
            etCode = findViewById(R.id.et_code);
            typeTitle = findViewById(R.id.tv_type_title);
            codeTitle = findViewById(R.id.tv_code_title);
            llUserInfo = findViewById(R.id.ll_userInfo);
            ivBack = findViewById(R.id.iv_back);
            llAuthentication = findViewById(R.id.ll_authentication);
            btnSubmitAuthentication = findViewById(R.id.btn_submit_authentication);
            llInvoiceInfoContainer.setVisibility(View.VISIBLE);
//            btnSubmit.setText("信息认证");
            btnSubmitAuthentication.observer(etType, etCode);
            switchSubmitBtn(true);
            btnSubmitAuthentication.setOnItemClickListener(isFlag -> btnSubmitAuthentication.setEnabled(isFlag));
            initProgress();
            progressView.setSelected(progressPosition);
            setForceSetting(typeTitle);
            setForceSetting(codeTitle);
            ivBack.setOnClickListener(v -> back());
            btnSubmitAuthentication.setOnClickListener(v -> authentication());
        } else {
            setTitle("资质变更");
        }
        editChangeListener();
        getBasicInfo();
    }

    /**
     * 验证编码的格式
     * @param code
     * @return
     */
    private boolean handleCodeFormat(String code) {
        if (code == null) return false;
        String str = "^[A-Z0-9-]+$";
        return Pattern.compile(str).matcher(code).matches();
    }

    /**
     * 切换提交按钮
     * @param isAuthentication 是否是认证
     */
    private void switchSubmitBtn(boolean isAuthentication) {
        btnSubmitAuthentication.setVisibility(isAuthentication? View.VISIBLE: View.GONE);
        btnSubmit.setVisibility(isAuthentication? View.GONE: View.VISIBLE);
    }

    /**
     * 设置编码最大长度
     */
    private void handleCodeMaxLengthAndHint() {
        if (codeType) {
            //true: 营业执照编码
            etCode.setFilters(new InputFilter[]{new InputFilter.LengthFilter(18)});
            etCode.setHint("请填写营业执照编码");
        } else {
            //false:医疗机构执业许可证编码
            etCode.setFilters(new InputFilter[]{new InputFilter.LengthFilter(23)});
            etCode.setHint("请填写医疗机构执业许可证编码");
        }
    }

    /**
     * 设置必选
     * @param tv
     */
    private void setForceSetting(TextView tv) {
        if (tv == null) return;
        if (tv.getText() == null) return;
        if (tv.getText().toString().length() == 0) return;
        String titleStr = tv.getText().toString();
        SpannableStringBuilder builder = new SpannableStringBuilder(titleStr);
        builder.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.color_ff2121)), 0, 1, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        tv.setText(builder);
    }

    /**
     * 是否是首营资质
     * @return
     */
    private boolean isAptitudeFirst() {
        return licenseStatus == APTITUDE_FIRSTTYPE;
    }

    /**
     * 认证
     */
    private void authentication() {
        if (etType.getText() == null || TextUtils.isEmpty(etType.getText().toString())) {
            ToastUtils.showShort("请填写企业类型");
            return;
        }
        if (etCode.getText() == null || TextUtils.isEmpty(etCode.getText().toString())) {
            ToastUtils.showShort(codeType? "请填写营业执照编码": "请填写医疗机构执业许可证编码");
            return;
        }

//        if (codeType && etCode.getText().toString().length() != 18) {
//            ToastUtils.showShort("请输入18个字符的营业执照编码");
//            return;
//        }
//
//        if (!codeType && etCode.getText().toString().length() != 23) {
//            ToastUtils.showShort("请输入23个字符的医疗机构执业许可证编码");
//            return;
//        }

        if (!handleCodeFormat(etCode.getText().toString())) {
            ToastUtils.showShort("仅限输入大写字母，数字和“-”符号");
            return;
        }
        showProgress();
        RequestParams params = new RequestParams();
        params.put("id", merchantId);
        params.put("customerType", mCustomerTypeId + "");
        params.put("code", etCode.getText().toString());
        HttpManager.getInstance().post(AppNetConfig.APITUDE_AUTHENTICTION, params, new BaseResponse<AptitudeAthentication>(){
            @Override
            public void onSuccess(String content, BaseBean<AptitudeAthentication> obj, AptitudeAthentication s) {
                super.onSuccess(content, obj, s);
                dismissProgress();
                if (obj != null && obj.isSuccess()) {
                    if (s != null) {
                        if (AddAptitudeBasicInfoActivity.this.isDestroy) return;
                        if (!TextUtils.isEmpty(s.getOccupyMobile()) && !TextUtils.isEmpty(s.getOccupyRealName())) {
                            // 编码占用用户和编码占用用户的电话都不存在的情况弹框提示
                            String message = "";
                            String aptutideCodeType = codeType? "营业执照编码": "医疗机构执业许可证编码";
                            if (s.getPhoneRemark()) {
                                message = aptutideCodeType + "已被\"" + s.getOccupyRealName() + "\"占用，是否联系您的专属销售找回";
                            } else {
                                message = aptutideCodeType + "已被\"" + s.getOccupyRealName() + "\"占用，是否联系客服找回";
                            }
                            new AlertDialogEx(AddAptitudeBasicInfoActivity.this)
                                    .setTitle("")
                                    .setMessage(message)
                                    .setCancelButton("取消", (dialog, button) -> {
                                        dialog.dismiss();
                                    })
                                    .setConfirmButton("呼叫", (dialog, button) -> {
                                        RoutersUtils.telKefu(true, s.getOccupyMobile(), "呼叫:");
                                    }).show();
                        } else {
                            progressPosition = 1;
                            progressView.setSelected(progressPosition);
//                            btnSubmit.setText("下一步");
                            switchSubmitBtn(false);
                            llUserInfo.setVisibility(View.VISIBLE);
                            llAuthentication.setVisibility(View.GONE);
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                dismissProgress();
            }
        });
    }

    private void initProgress() {
        List<AptitudeProgressEntry> entrys = new ArrayList<>();
        entrys.add(new AptitudeProgressEntry("信息认证"));
        entrys.add(new AptitudeProgressEntry("信息确认"));
        entrys.add(new AptitudeProgressEntry("上传资质照片"));
        progressView.setData(entrys);
    }

    private void getBasicInfo() {
        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        HttpManager.getInstance().post(AppNetConfig.QUERY_LICENSE_AUDIT_BASICINFO, params, new BaseResponse<AptitudeBasicInfoBean>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

            @Override
            public void onSuccess(String content, BaseBean<AptitudeBasicInfoBean> baseBean, AptitudeBasicInfoBean data) {
                dismissProgress();
                if (baseBean != null && baseBean.isSuccess()) {
                    if (data != null && data.info != null) {
                        AptitudeBasicInfo basicInfo = data.info;
                        etName.setText(basicInfo.getRealName());
                        etType.setText(basicInfo.getCustomerTypeName());
                        //非首营回显地址
//                        etAddress.setText(basicInfo.getArea());
//                        etDetailAddress.setText(basicInfo.getAddress());
                        mCustomerTypeId = basicInfo.getCustomerType();
                        firstCustomerType = mCustomerTypeId;
                        firstCustomerTypeName = basicInfo.getCustomerTypeName();
                        mCurrentProvinceId = basicInfo.getProvinceId() + "";
                        mCurrentCityId = basicInfo.getCityId() + "";
                        mCurrentDistrictId = basicInfo.getDistrictId() + "";
                        mCurrentStreetId = basicInfo.getStreetId() + "";
                        if (isAptitudeFirst()) {
                            codeType = basicInfo.remark;
                            setCodeTitle(basicInfo.remark, codeTitle);
                            handleCodeMaxLengthAndHint();
                            if (isEdit == 1) {
                                etCode.setText(basicInfo.code);
                            }
                        }
                        if (invoiceTypeList != null && invoiceTypeList.size() > 0) {
                            invoiceTypeList.clear();
                        }
                        if (data.invoiceList != null && data.invoiceList.size() > 0) {
                            mSelectedInvoiceType = data.invoiceList.get(0).getId();
                            etInvoiceType.setText(data.invoiceList.get(0).getName());
                        }
                        invoiceTypeList.addAll(data.invoiceList);

                    }
                }
            }
        });
    }

    private void setCodeTitle(boolean isBusiness, TextView tv) {
        codeType = isBusiness;
        if (isBusiness) {
            tv.setText(getResources().getString(R.string.str_aptitude_add_basic_info_company_name_code));
        } else {
            tv.setText(getResources().getString(R.string.str_aptitude_add_basic_info_company_name_code2));
        }
        setForceSetting(tv);
    }

    @OnClick({R.id.iv_top_tips_delete, R.id.et_type, R.id.et_invoice_type, R.id.et_address, R.id.btn_submit})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_top_tips_delete://顶部提示点击隐藏
                llTopTips.setVisibility(View.GONE);
                break;
            case R.id.et_type:
                hideSoftInput();
                showAptitudeDialog();
                break;
            case R.id.et_invoice_type:
                hideSoftInput();
                selectInvoiceType();
                break;
            case R.id.et_address:
                hideSoftInput();
                if (dialog == null) {
                    dialog = new BottomDialog(AddAptitudeBasicInfoActivity.this);
                }
                dialog.show();
                dialog.setOnAddressSelectedListener(AddAptitudeBasicInfoActivity.this);
                dialog.setAddressProvider(addressListener);
                break;
            case R.id.btn_submit:
                HashMap<String, String> map = new HashMap<>();
                map.put("accountId", SpUtil.getAccountId());
                XyyIoUtil.track("action_addQualification_confirm_click", map);
                submitData();
                break;
        }
    }

    private void initLicense(int customerType) {
        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        params.put("customerType", customerType + "");
        if (isEdit == 1) {
            params.put("type", "2");//首营还是变更
        } else {
            params.put("type", licenseStatus + "");//首营还是变更
        }
        //机构id
        params.put("orgCode", ecOrgCode);
        params.put("remark", isEdit == 1? "true": "false");//是否拦截,从首营资质一审驳回编辑页进入后传false，防止存在该类工单后后台不允许提交
        HttpManager.getInstance().post(AppNetConfig.INIT_LICENSE_AUDIT_DETAIL, params, new BaseResponse<AptitudeInitBean>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

            @Override
            public void onSuccess(String content, BaseBean<AptitudeInitBean> baseBean, AptitudeInitBean data) {
                dismissProgress();
                if (baseBean != null && baseBean.isSuccess()) {
                    if (data != null) {
                        boolean isChange = data.licenseAudit != null;
                        //bean.licenseAudit != null表示可修改，否则只能添加
                        jumpNext(isChange, data, customerType);
                    }
                }
            }
        });
    }

    private void jumpNext(boolean isChange, AptitudeInitBean data, int customerType) {
        if (data == null || data.necessaryLicenceList == null || data.necessaryLicenceList.isEmpty() || data.optionalLicenceList == null || data.optionalLicenceList.isEmpty()) {
            ToastUtils.showShort(R.string.text_aptitude_failure);
            return;
        }
        ArrayList<LicenceBean> mNecessary = new ArrayList<>();
        ArrayList<LicenceBean> mOptional = new ArrayList<>();
        LicenceBean bean;
        //修改状态下，需要把上次已添加的照片同步过来，比如之前保存到了草稿，这次需要修改
        for (AptitudeInitBean.Licence licence : data.necessaryLicenceList) {
            bean = new LicenceBean(licence.code, licence.name, licence.isRequired, licence.templateUrl, licence.status, licence.listImgUrls,licence.remark);
            mNecessary.add(bean);
        }
        for (AptitudeInitBean.Licence licence : data.optionalLicenceList) {
            bean = new LicenceBean(licence.code, licence.name, licence.isRequired, licence.templateUrl, licence.status, licence.listImgUrls,licence.remark);
            mOptional.add(bean);
        }
        if (isChange && data.licenseAuditImgList != null) {
            HashMap<String, AptitudeInitBean.ImageItem> record = new HashMap<>();
            for (AptitudeInitBean.ImageItem item : data.licenseAuditImgList) {
                if (item == null) {
                    continue;
                }
                record.put(item.licenseCode, item);
            }
            AptitudeInitBean.ImageItem image;
            for (LicenceBean licence : mOptional) {
                image = record.get(licence.categoryCode);
                if (image != null) {
                    licence.xyyEntrusCode = image.xyyEntrusCode;
                    licence.xyyEntrusValidateTime = image.xyyEntrusValidateTime;
                }
            }
        }
        String aptitudeId = isChange ? data.licenseAudit.applicationNumber : "-1";
        boolean isAddType = licenseStatus == APTITUDE_FIRSTTYPE;

        if (isEdit != 1) {
            AddAptitudeActivity.startActivity(
                    getMySelf(),
                    customerType + "",
                    merchantId,
                    aptitudeId,
                    null,
                    isAddType,
                    true,
                    mNecessary,
                    mOptional,
                    data.getRemark(),
                    data.tempRemark,
                    "",
                    "",
                    0,
                    "",
                    "",
                    APTITUDE_LIST_ACTIVITY,
                    extrasBean,
                    isAddType);
        } else {
            AddAptitudeActivity.startActivity(
                    getMySelf(),
                    customerType+"",
                    merchantId,
                    extra.aptitudeId,
                    ecOrgCode,
                    extra.isAdd,
                    true,
//                    extra.mNecessary,
//                    extra.mOptional,
                    mNecessary,
                    mOptional,
                    extra.remark,
                    extra.tempRemark,
                    extra.code,
                    extra.status,
                    extra.statusCode,
                    extra.licenseAuditId,
                    extra.time,
                    APTITUDE_DETAIL_ACTIVITY,
                    extrasBean,
                    true);
        }
    }


    /**
     * 选择发票类型
     */
    private void selectInvoiceType() {
        if (licenseStatus == APTITUDE_FIRSTTYPE && invoiceTypeList != null && invoiceTypeList.size() > 0) {
            showInvoiceTypeDialog(invoiceTypeList);
        }
    }

    /**
     * 展示资质类型选择
     */
    private void showAptitudeDialog() {//进入资质变更，也要弹出客户类型选择项
        if (customerTypeList != null && customerTypeList.size() > 0) {
            showCustomerTypeDialog(customerTypeList);
        } else {
            getCustomerType();
        }
    }

    private void showBackDialog(int index, CustomerTypeBean customerTypeBean) {
        AlertDialogEx dialogEx = new AlertDialogEx(this);
        dialogEx.setTitle("")
                .setMessage("变更资质类型将覆盖原资质，\n是否替换？")
                .setCancelButton("取消", (AlertDialogEx.OnClickListener) (dialog, button) -> {
                })
                .setConfirmButton("替换", (AlertDialogEx.OnClickListener) (dialog, button) -> {
                    switchCode(index, customerTypeBean);
                })
                .show();
    }

    private void getCustomerType() {//获取客户类型
        showProgress();
        RequestParams params = new RequestParams();
        HttpManager.getInstance().post(AppNetConfig.GET_CUSTOMER_TYPE_ALL, params, new BaseResponse<List<CustomerTypeBean>>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

            @Override
            public void onSuccess(String content, BaseBean<List<CustomerTypeBean>> baseBean, List<CustomerTypeBean> data) {
                dismissProgress();
                if (baseBean != null && baseBean.isSuccess()) {
                    if (data != null) {

                        if (data != null && data.size() > 0) {
                            customerTypeList.clear();
                            customerTypeList.addAll(data);
                            setCustomerTypeDefaultSelectedIndex(firstCustomerType);
                            showCustomerTypeDialog(customerTypeList);
                        } else {
                            ToastUtils.showShort("获取客户类型数据为空");
                        }
                    }
                }
            }
        });
    }

    /**
     * 展示发票类型选择
     */
    private void showInvoiceTypeDialog(List<AptitudeInvoiceTypeBean> invoiceTypeList) {
        String title = "发票类型";
        PickerManager.showSingleSelectPicker(getMySelf(), title, invoiceTypeList, mSelectedInvoiceTypeIndex, new PickerManager.OnOptionsSelectCallback2<AptitudeInvoiceTypeBean>() {
            @Override
            public void onOptionsSelect(int index, AptitudeInvoiceTypeBean invoiceBean) {
                mSelectedInvoiceTypeIndex = index;
                mSelectedInvoiceType = invoiceBean.getId();
                etInvoiceType.setText(invoiceBean.getName());
            }
        }, new PickerManager.Adapter<AptitudeInvoiceTypeBean>() {
            @Override
            public String getStr(AptitudeInvoiceTypeBean invoiceTypeBean) {
                return invoiceTypeBean.getName();
            }
        });
    }

    /**
     * 展示客户类型选择
     */
    private void showCustomerTypeDialog(List<CustomerTypeBean> customerTypeList) {
        String title = "企业类型";
        PickerManager.showSingleSelectPicker(getMySelf(), title, customerTypeList, mCustomerTypeIndex, new PickerManager.OnOptionsSelectCallback2<CustomerTypeBean>() {
            @Override
            public void onOptionsSelect(int index, CustomerTypeBean customerTypeBean) {
                if (firstCustomerType != 0 && customerTypeBean.getId() != firstCustomerType) {//添加首营不会弹这个提示，修改首营和资质变更提示客户类型覆盖
                    showBackDialog(index, customerTypeBean);
                } else {
                    switchCode(index, customerTypeBean);
                }
            }
        }, new PickerManager.Adapter<CustomerTypeBean>() {
            @Override
            public String getStr(CustomerTypeBean customerTypeBean) {
                return customerTypeBean.getName();
            }
        });
    }

    private void switchCode(int index, CustomerTypeBean customerTypeBean) {
        mCustomerTypeIndex = index;
        mCustomerTypeId = customerTypeBean.getId();
        etType.setText(customerTypeBean.getName());
        if (isAptitudeFirst()) {
            setCodeTitle(customerTypeBean.remark, codeTitle);
            handleCodeMaxLengthAndHint();
            etCode.setText("");
        }
    }

    //设置默认上次选择的客户类型下标
    private void setCustomerTypeDefaultSelectedIndex(int firstType) {
        if (customerTypeList != null && customerTypeList.size() > 0) {
            for (int i = 0; i < customerTypeList.size(); i++) {
                if (customerTypeList.get(i).getId() == firstType) {
                    mCustomerTypeIndex = i;
                }
            }
        }
    }

    private void submitData() {
        String companyName = etName.getText().toString().trim();
        String companyType = etType.getText().toString().trim();
        String address = etAddress.getText().toString().trim();
        String detailAddress = etDetailAddress.getText().toString().trim();
        if (TextUtils.isEmpty(companyName)) {
            ToastUtils.showShort("请输入企业名称");
            return;
        }
        if (TextUtils.isEmpty(companyType)) {
            ToastUtils.showShort("请选择企业类型");
            return;
        }
        if (TextUtils.isEmpty(address)) {
            ToastUtils.showShort("请选择所在区域地址");
            return;
        }
        if (TextUtils.isEmpty(detailAddress)) {
            ToastUtils.showShort("请输入详细地址");
            return;
        }
        //企业信息和收货地址信息需要带到下一个页面提交到接口 组装数据
        extrasBean = new AptitudeBasicInfoExtrasBean(companyName, companyType, detailAddress,
                mCurrentProviceName, mCurrentCityName, mCurrentDistrictName, mCurrentStreetName,
                mCurrentProvinceId, mCurrentCityId, mCurrentDistrictId, mCurrentStreetId);
        // 如果是首营资质，添加发票类型
        extrasBean.invoiceType = mSelectedInvoiceType;

        initLicense(mCustomerTypeId);
    }

    private void editChangeListener() {
        UiUtils.setEditTextInhibitInputSpace(etDetailAddress);
        btnSubmit.observer(etName, etType, etAddress, etDetailAddress);
        btnSubmit.setOnItemClickListener(isFlag -> btnSubmit.setEnabled(isFlag));
    }

    AddressProvider addressListener = new AddressProvider() {
        @Override
        public void provideProvinces(final AddressReceiver<Province> addressReceiver) {
            RequestParams params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                    .addParam("parentId", "0").build();
            HttpManager.getInstance().post(params, new BaseResponse<List<Province>>() {
                @Override
                public void onSuccess(String content, BaseBean<List<Province>> bean, List<Province> data) {
                    if (bean == null || !bean.isSuccess()) {
                        return;
                    }
                    List<Province> provinces = bean.data;
                    addressReceiver.send(provinces);
                }
            });
        }

        @Override
        public void provideCitiesWith(String provinceId, final AddressReceiver<Province> addressReceiver) {
            RequestParams params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                    .addParam("parentId", "" + provinceId).build();
            HttpManager.getInstance().post(params, new BaseResponse<List<Province>>() {
                @Override
                public void onSuccess(String content, BaseBean<List<Province>> bean, List<Province> data) {
                    if (bean == null || !bean.isSuccess()) {
                        return;
                    }
                    List<Province> provinces = bean.data;
                    addressReceiver.send(provinces);
                }
            });
        }

        @Override
        public void provideCountiesWith(String cityId, final AddressReceiver<Province> addressReceiver) {
            RequestParams params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                    .addParam("parentId", "" + cityId).build();
            HttpManager.getInstance().post(params, new BaseResponse<List<Province>>() {
                @Override
                public void onSuccess(String content, BaseBean<List<Province>> bean, List<Province> data) {
                    if (bean == null || !bean.isSuccess()) {
                        return;
                    }
                    List<Province> provinces = bean.data;
                    addressReceiver.send(provinces);
                }
            });
        }

        @Override
        public void provideStreetsWith(String countyId, AddressReceiver<Province> addressReceiver) {
            // 2019/11/14 优化省市区街道
            RequestParams params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                    .addParam("parentId", "" + countyId).build();
            HttpManager.getInstance().post(params, new BaseResponse<List<Province>>() {
                @Override
                public void onSuccess(String content, BaseBean<List<Province>> bean, List<Province> data) {
                    if (bean == null || !bean.isSuccess()) {
                        return;
                    }
                    List<Province> provinces = bean.data;
                    addressReceiver.send(provinces);
                }
            });
        }
    };

    @Override
    public void onAddressSelected(Province province, Province city, Province county, Province street) {
        if (dialog != null) {
            try {
                dialog.dismiss();
            } catch (Exception e) {
            }
        }
        if (province == null || city == null) {
            return;
        }

        String address = province.areaName + city.areaName + (county == null ? "" : county.areaName + (street == null ? "" : street.areaName));
        etAddress.setText(address);

        mCurrentProviceName = province.areaName;
        mCurrentCityName = city.areaName;
        mCurrentDistrictName = county == null ? "" : county.areaName;
        mCurrentStreetName = county == null ? "" : (street == null ? "" : street.areaName);

        mCurrentProvinceId = province.areaCode;
        mCurrentCityId = city.areaCode;
        mCurrentDistrictId = county == null ? "" : county.areaCode;
        mCurrentStreetId = county == null ? "" : (street == null ? "" : street.areaCode);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            back();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void back() {
        if (from == 1) {
            RoutersUtils.open("ybmpage://main");
            HashMap<String, String> map = new HashMap<>();
            map.put("accountId", SpUtil.getAccountId());
            XyyIoUtil.track("action_addQualification_back_click", map);
            finish();
            return;
        }
        if (progressPosition == 0) finish();
        else if(progressPosition == 1) {
            progressPosition = 0;
            progressView.setSelected(progressPosition);
            llAuthentication.setVisibility(View.VISIBLE);
            llUserInfo.setVisibility(View.GONE);
//            btnSubmit.setText("信息认证");
            switchSubmitBtn(true);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == APTITUDE_REQUEST_CODE && resultCode == RESULT_OK) {//提交资质图片成功后关闭这个页面
            setResult(RESULT_OK);
            finish();
        }
    }
}
