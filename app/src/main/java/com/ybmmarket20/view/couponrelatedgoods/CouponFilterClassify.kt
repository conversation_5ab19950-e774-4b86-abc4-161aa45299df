package com.ybmmarket20.view.couponrelatedgoods

import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import com.ybmmarket20.R
import com.ybmmarket20.view.LeftPopWindow
import com.ybmmarket20.view.LeftPopWindow.Listener

/**
 * 凑单筛选
 */
class CouponFilterClassify {

    private var mPopupWindow: LeftPopWindow<Nothing?>? = null
    private var mSelectListener: ISelectListener? = null
    //保存服务
    private val mServiceMap = mutableMapOf<String, View>()
    //药品类型
    private val mDrugClassifyMap = mutableMapOf<String, View>()
    //每次调用show()恢复缓存数据，调用confirm()缓存数据
    private val mCacheResultMap = mutableMapOf<String, Boolean>()

    fun init() {
        mPopupWindow = object : LeftPopWindow<Nothing?>(R.layout.pop_filter_classify) {
            override fun initView(contentView: View) {
                val tvAvailable = contentView.findViewById<TextView>(R.id.tvAvailable)
                val tvExpressSF = contentView.findViewById<TextView>(R.id.tvExpressSF)
                val tvExpressJD = contentView.findViewById<TextView>(R.id.tvExpressJD)
                val tvClassA = contentView.findViewById<TextView>(R.id.tvClassA)
                val tvClassB = contentView.findViewById<TextView>(R.id.tvClassB)
                val tvClassRX = contentView.findViewById<TextView>(R.id.tvClassRX)
                val tvClassOthers = contentView.findViewById<TextView>(R.id.tvClassOthers)
                val btnReset = contentView.findViewById<Button>(R.id.btnReset)
                val btnConfirm = contentView.findViewById<Button>(R.id.btnConfirm)
                val ivSearchFilterClose = contentView.findViewById<ImageView>(R.id.ivSearchFilterClose)
                val viewSpace = contentView.findViewById<View>(R.id.viewSpace)
                btnReset.setOnClickListener(::reset)
                btnConfirm.setOnClickListener(::confirm)
                ivSearchFilterClose.setOnClickListener(::clickClose)
                viewSpace.setOnClickListener(::clickClose)
                initServiceStatus(tvAvailable, "hasStock")
                initServiceStatus(tvExpressSF, "isSupportSfExpress")
                initServiceStatus(tvExpressJD, "isSupportJdExpress")

                initDrugClassifyStatus(tvClassA, "1")
                initDrugClassifyStatus(tvClassB, "2")
                initDrugClassifyStatus(tvClassRX, "3")
                initDrugClassifyStatus(tvClassOthers, "4")

            }
        }
        mPopupWindow?.setListener(object: Listener<Nothing?>{
            override fun onDismiss() {
                //回调参数为缓存(如果是确定后的缓存则是选中的结果)的选中项
                mSelectListener?.onDismiss(mCacheResultMap.filter { it.value })
                mPopupWindow?.setDim(1f)
            }

            override fun onResult(t: Nothing?) {}
        })
    }

    fun show() {
        recoveryData()
        mPopupWindow?.show()
    }

    fun setOnSelectListener(selectListener: ISelectListener) {
        mSelectListener = selectListener
    }

    /**
     * 恢复打开时的数据
     */
    private fun recoveryData() {
        handleMapData(mServiceMap, mDrugClassifyMap) {
            val key = it.key
            it.value.isActivated = mCacheResultMap[key]?: false
        }
    }

    /**
     * 缓存数据
     */
    private fun cacheData() {
        handleMapData(mServiceMap, mDrugClassifyMap) {
            mCacheResultMap[it.key] = it.value.isActivated
        }
    }

    /**
     * 初始化服务
     */
    private fun initServiceStatus(view: View, tag: String) {
        view.setTag(view.id, tag)
        mServiceMap[tag] = view
        view.setOnClickListener {
            mServiceMap[tag] = view
            view.isActivated = !mServiceMap[tag]!!.isActivated
        }
    }

    /**
     * 初始化药品类型
     */
    private fun initDrugClassifyStatus(view: View, tag: String) {
        view.setTag(view.id, tag)
        mDrugClassifyMap[tag] = view
        view.setOnClickListener {
            mDrugClassifyMap[tag] = view
            view.isActivated = !mDrugClassifyMap[tag]!!.isActivated
        }
    }

    /**
     * 处理数据
     */
    private fun handleMapData(vararg maps: MutableMap<String, View>, block: (map: Map.Entry<String, View>) -> Unit) {
        maps.forEach {
            it.map(block)
        }
    }

    /**
     * 重置
     */
    private fun reset(v: View) {
        handleMapData(mServiceMap, mDrugClassifyMap) {
            it.value.isActivated = false
        }
    }

    /**
     * 确定
     */
    private fun confirm(v: View) {
        //保存当前的选中状态
        cacheData()
        mSelectListener?.onResult(getResult())
        mPopupWindow?.dismiss()
    }

    /**
     * 点击关闭
     */
    private fun clickClose(v: View) {
        mPopupWindow?.dismiss()
    }

    /**
     * 获取结果
     */
    private fun getResult(): MutableMap<String, String> {
        val resultMap = mutableMapOf<String, String>()
        //服务选项参数
        mServiceMap.forEach {
            if (it.value.isActivated){
                resultMap[it.key] = "1"
            }else{
                resultMap[it.key] = ""
            }
        }


        //药品类型参数
        mDrugClassifyMap.filter {
            it.value.isActivated
        }.map {
            it.key
        }.joinToString(",")
            .let {
                resultMap["drugClassificationsStr"] = it
            }
        return resultMap
    }

    interface ISelectListener {
        fun onDismiss(params: Map<String, Boolean>)
        fun onResult(params: Map<String, String>)
    }

}