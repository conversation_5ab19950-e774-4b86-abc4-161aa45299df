package com.ybmmarket20.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ybmmarket20.R
import com.ybmmarket20.bean.FieldMapping
import com.ybmmarket20.bean.InvoiceEditHistoryBean

/**
 * 发票编辑历史项自定义View
 * author: hcq
 * date: 2025/9/23
 * desc: 封装发票编辑历史显示逻辑，支持根据数据自动显示隐藏字段
 */
class InvoiceEditHistoryItem @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    // 各个字段的LinearLayout容器
    private val llInvoiceType: LinearLayout
    private val llCompanyName: LinearLayout
    private val llTaxNumber: LinearLayout
    private val llAddress: LinearLayout
    private val llPhone: LinearLayout
    private val llBankName: LinearLayout
    private val llBankAccount: LinearLayout
    private val llEditTime: LinearLayout
    private val llStatusDesc: LinearLayout

    // 各个字段的TextView
    private val tvInvoiceType: TextView
    private val tvCompanyName: TextView
    private val tvTaxNumber: TextView
    private val tvAddress: TextView
    private val tvPhone: TextView
    private val tvBankName: TextView
    private val tvBankAccount: TextView
    private val tvEditTime: TextView
    private val tvStatusDesc: TextView

    init {
        LayoutInflater.from(context).inflate(R.layout.item_invoice_edit_history, this, true)

        // 初始化LinearLayout容器
        llInvoiceType = findViewById(R.id.ll_invoice_type)
        llCompanyName = findViewById(R.id.ll_company_name)
        llTaxNumber = findViewById(R.id.ll_tax_number)
        llAddress = findViewById(R.id.ll_address)
        llPhone = findViewById(R.id.ll_phone)
        llBankName = findViewById(R.id.ll_bank_name)
        llBankAccount = findViewById(R.id.ll_bank_account)
        llEditTime = findViewById(R.id.ll_edit_time)
        llStatusDesc = findViewById(R.id.ll_status_desc)

        // 初始化TextView
        tvInvoiceType = findViewById(R.id.tv_invoice_type)
        tvCompanyName = findViewById(R.id.tv_company_name)
        tvTaxNumber = findViewById(R.id.tv_tax_number)
        tvAddress = findViewById(R.id.tv_address)
        tvPhone = findViewById(R.id.tv_phone)
        tvBankName = findViewById(R.id.tv_bank_name)
        tvBankAccount = findViewById(R.id.tv_bank_account)
        tvEditTime = findViewById(R.id.tv_edit_time)
        tvStatusDesc = findViewById(R.id.tv_status_desc)
    }


    /**
     * 绑定发票编辑历史数据
     * @param data 发票编辑历史数据
     */
    fun bindData(data: InvoiceEditHistoryBean?) {
        if (data == null) {
            visibility = View.GONE
            return
        }

        visibility = View.VISIBLE

        // 定义字段映射关系
        val fieldMappings = listOf(
            FieldMapping(data.invoiceType, llInvoiceType, tvInvoiceType),
            FieldMapping(data.companyName, llCompanyName, tvCompanyName),
            FieldMapping(data.taxNumber, llTaxNumber, tvTaxNumber),
            FieldMapping(data.address, llAddress, tvAddress),
            FieldMapping(data.phone, llPhone, tvPhone),
            FieldMapping(data.bankName, llBankName, tvBankName),
            FieldMapping(data.bankAccount, llBankAccount, tvBankAccount),
            FieldMapping(data.editTime, llEditTime, tvEditTime),
            FieldMapping(data.editStatusName, llStatusDesc, tvStatusDesc)
        )

        // 遍历处理每个字段
        fieldMappings.forEach { (value, container, textView) ->
            if (value.isNullOrEmpty()) {
                container.visibility = View.GONE
            } else {
                container.visibility = View.VISIBLE
                textView.text = value
            }
        }
    }
}