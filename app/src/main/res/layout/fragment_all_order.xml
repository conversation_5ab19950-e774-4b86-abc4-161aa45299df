<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_height="49dp"
        android:orientation="horizontal"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="15dp"
        android:layout_width="match_parent">

        <com.ybmmarket20.common.widget.RoundConstraintLayout
            android:id="@+id/cl_search"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:layout_gravity="center_vertical"
            app:rv_backgroundColor="@color/colors_f5f5f8"
            app:rv_cornerRadius="6dp"
            android:layout_height="37dp">

            <ImageView
                android:id="@+id/iv_search"
                android:layout_width="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginStart="10dp"
                android:src="@drawable/icon_order_search"
                android:layout_height="16dp"/>

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="0dp"
                android:text="搜索商品/发票/订单编号"
                android:textColor="@color/text_color_999999"
                android:textSize="14dp"
                app:layout_constraintStart_toEndOf="@id/iv_search"
                android:layout_marginStart="6dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_height="wrap_content"/>

        </com.ybmmarket20.common.widget.RoundConstraintLayout>
        <LinearLayout
            android:id="@+id/llFilter"
            android:layout_width="wrap_content"
            android:orientation="vertical"
            android:paddingEnd="@dimen/pabr_dimen9dp"
            android:gravity="center"
            android:paddingHorizontal="10dp"
            android:layout_height="match_parent">

            <ImageView
                android:src="@drawable/icon_order_filter"
                android:layout_width="21dp"
                android:layout_height="21dp"/>

            <TextView
                android:text="筛选"
                android:textSize="11dp"
                android:textColor="@color/text_color_333333"
                android:layout_width="wrap_content"
                android:layout_marginTop="2dp"
                android:layout_height="wrap_content"/>

        </LinearLayout>
        <LinearLayout
            android:id="@+id/llInVoice"
            android:layout_width="wrap_content"
            android:orientation="vertical"
            android:paddingEnd="@dimen/pabr_dimen9dp"
            android:gravity="center"
            android:paddingHorizontal="10dp"
            android:layout_height="match_parent">

            <ImageView
                android:src="@drawable/icon_order_invoice"
                android:layout_width="21dp"
                android:layout_height="21dp"/>

            <TextView
                android:text="发票"
                android:textSize="11dp"
                android:textColor="@color/text_color_333333"
                android:layout_width="wrap_content"
                android:layout_marginTop="2dp"
                android:layout_height="wrap_content"/>

        </LinearLayout>
        <LinearLayout
            android:id="@+id/ll_bill"
            android:layout_width="wrap_content"
            android:paddingStart="@dimen/pabr_dimen9dp"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/iv_bill"
                android:src="@drawable/icon_order_bill"
                android:layout_width="21dp"
                android:layout_height="21dp"/>

            <TextView
                android:id="@+id/tv_bill"
                android:text="对账单"
                android:textSize="11dp"
                android:textColor="@color/text_color_333333"
                android:layout_width="wrap_content"
                android:layout_marginTop="2dp"
                android:layout_height="wrap_content"/>

        </LinearLayout>

    </LinearLayout>

    <com.flyco.tablayout.SlidingTabLayout
        android:id="@+id/ps_tab"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginTop="6dp"
        android:background="@color/white"
        android:orientation="horizontal"
        app:layout_scrollFlags="scroll|enterAlways"
        app:tl_indicator_color="@color/color_00B955"
        app:tl_indicator_corner_radius="2dp"
        app:tl_indicator_height="3dp"
        app:tl_indicator_width="21dp"
        app:tl_tab_space_equal="false"
        app:tl_textAllCaps="true"
        app:tl_textBold="SELECT"
        app:tl_tab_padding="18dp"
        app:tl_textSelectColor="@color/text_color_333333"
        app:tl_textSelectSize="16dp"
        app:tl_textUnselectColor="@color/text_color_333333"
        app:tl_textsize="16dp" />

    <com.ybmmarketkotlin.views.BannerOrderTopView
        android:id="@+id/order_banner"
        android:layout_width="match_parent"
        android:visibility="gone"
        android:layout_height="68dp" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.ybmmarket20.view.NoScrollViewPager
            android:id="@+id/vp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />
        <com.ybmmarket20.view.PushTipsBottomView
            android:id="@+id/viewPushTipsBottom"
            android:layout_width="match_parent"
            android:layout_height="38dp"
            android:layout_marginHorizontal="@dimen/pabr_dimen10dp"
            android:layout_marginBottom="@dimen/pabr_dimen10dp"
            android:layout_gravity="bottom"
            android:visibility="gone"/>
    </FrameLayout>

</LinearLayout>
